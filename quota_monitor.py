#!/usr/bin/env python3
"""
Google API Quota Monitor for TeleConnect RAG Bot
Helps track and manage API usage to avoid quota exhaustion
"""

import os
import json
import datetime
from pathlib import Path
from typing import Dict, Any
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class QuotaMonitor:
    """Monitor and track Google API quota usage"""
    
    def __init__(self, usage_file: str = "api_usage.json"):
        """
        Initialize quota monitor
        
        Args:
            usage_file: File to store usage statistics
        """
        self.usage_file = Path(usage_file)
        self.api_key = os.getenv("GOOGLE_API_KEY")
        
        # Load existing usage data
        self.usage_data = self._load_usage_data()
        
    def _load_usage_data(self) -> Dict[str, Any]:
        """Load usage data from file"""
        if self.usage_file.exists():
            try:
                with open(self.usage_file, 'r') as f:
                    return json.load(f)
            except Exception:
                pass
        
        # Default structure
        return {
            "daily_usage": {},
            "total_requests": 0,
            "last_reset": datetime.date.today().isoformat()
        }
    
    def _save_usage_data(self):
        """Save usage data to file"""
        try:
            with open(self.usage_file, 'w') as f:
                json.dump(self.usage_data, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save usage data: {e}")
    
    def record_request(self, model: str = "gemini-1.5-flash", success: bool = True):
        """
        Record an API request
        
        Args:
            model: Model name used
            success: Whether the request was successful
        """
        today = datetime.date.today().isoformat()
        
        # Reset daily usage if it's a new day
        if today != self.usage_data.get("last_reset"):
            self.usage_data["daily_usage"] = {}
            self.usage_data["last_reset"] = today
        
        # Initialize model tracking
        if model not in self.usage_data["daily_usage"]:
            self.usage_data["daily_usage"][model] = {
                "successful": 0,
                "failed": 0,
                "total": 0
            }
        
        # Record the request
        if success:
            self.usage_data["daily_usage"][model]["successful"] += 1
        else:
            self.usage_data["daily_usage"][model]["failed"] += 1
        
        self.usage_data["daily_usage"][model]["total"] += 1
        self.usage_data["total_requests"] += 1
        
        # Save updated data
        self._save_usage_data()
    
    def get_daily_usage(self, model: str = "gemini-1.5-flash") -> Dict[str, int]:
        """Get today's usage for a specific model"""
        today = datetime.date.today().isoformat()
        
        if today != self.usage_data.get("last_reset"):
            return {"successful": 0, "failed": 0, "total": 0}
        
        return self.usage_data["daily_usage"].get(model, {
            "successful": 0, "failed": 0, "total": 0
        })
    
    def check_quota_status(self, model: str = "gemini-1.5-flash") -> Dict[str, Any]:
        """
        Check current quota status
        
        Returns:
            Dictionary with quota information
        """
        usage = self.get_daily_usage(model)
        
        # Free tier limits (approximate)
        free_tier_limits = {
            "gemini-1.5-flash": 50,
            "gemini-1.5-pro": 50,
            "gemini-pro": 60
        }
        
        limit = free_tier_limits.get(model, 50)
        remaining = max(0, limit - usage["total"])
        percentage_used = (usage["total"] / limit) * 100 if limit > 0 else 0
        
        return {
            "model": model,
            "daily_limit": limit,
            "used_today": usage["total"],
            "successful_today": usage["successful"],
            "failed_today": usage["failed"],
            "remaining": remaining,
            "percentage_used": percentage_used,
            "quota_exhausted": remaining <= 0,
            "warning_threshold": percentage_used >= 80
        }
    
    def test_api_availability(self) -> bool:
        """
        Test if the API is currently available
        
        Returns:
            True if API is available, False otherwise
        """
        if not self.api_key:
            return False
        
        try:
            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel("gemini-1.5-flash")
            
            # Try a minimal request
            response = model.generate_content("Hi", 
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=1,
                    temperature=0
                ))
            
            self.record_request("gemini-1.5-flash", True)
            return True
            
        except Exception as e:
            error_str = str(e).lower()
            if "quota" in error_str or "429" in str(e):
                self.record_request("gemini-1.5-flash", False)
                return False
            else:
                # Other errors might be temporary
                return False
    
    def print_status_report(self):
        """Print a detailed status report"""
        print("=== Google API Quota Status ===")
        
        models = ["gemini-1.5-flash", "gemini-1.5-pro"]
        
        for model in models:
            status = self.check_quota_status(model)
            print(f"\n{model}:")
            print(f"  Daily Limit: {status['daily_limit']}")
            print(f"  Used Today: {status['used_today']} ({status['percentage_used']:.1f}%)")
            print(f"  Successful: {status['successful_today']}")
            print(f"  Failed: {status['failed_today']}")
            print(f"  Remaining: {status['remaining']}")
            
            if status['quota_exhausted']:
                print(f"  Status: ❌ QUOTA EXHAUSTED")
            elif status['warning_threshold']:
                print(f"  Status: ⚠️  WARNING - Near limit")
            else:
                print(f"  Status: ✅ OK")
        
        print(f"\nTotal Requests (All Time): {self.usage_data['total_requests']}")
        print(f"Last Reset: {self.usage_data['last_reset']}")

def main():
    """Main function for command-line usage"""
    monitor = QuotaMonitor()
    
    print("Testing API availability...")
    available = monitor.test_api_availability()
    print(f"API Available: {'✅ Yes' if available else '❌ No'}")
    
    print()
    monitor.print_status_report()

if __name__ == "__main__":
    main()
