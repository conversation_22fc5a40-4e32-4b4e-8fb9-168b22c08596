"""
Gemini AI Integration for TeleConnect Customer Support RAG Bot
Handles LLM responses using Google's Gemini AI Flash 1.5 model
"""

import os
import google.generativeai as genai
from typing import List, Dict, Any, Optional
import logging
from dotenv import load_dotenv
import json
from prompt_templates import TelecomPromptTemplates

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelecomGeminiLLM:
    """
    Gemini AI service for generating responses in customer support context
    """
    
    def __init__(self, api_key: Optional[str] = None, model_name: str = "gemini-1.5-flash"):
        """
        Initialize Gemini AI service
        
        Args:
            api_key: Google API key (if not provided, will use environment variable)
            model_name: Gemini model to use
        """
        self.api_key = api_key or os.getenv("GOOGLE_API_KEY")
        self.model_name = model_name
        
        if not self.api_key:
            raise ValueError("Google API key is required. Set GOOGLE_API_KEY environment variable or pass api_key parameter.")
        
        # Configure Gemini AI
        genai.configure(api_key=self.api_key)
        
        # Initialize model
        self.model = genai.GenerativeModel(model_name)

        # Initialize prompt templates
        self.prompt_templates = TelecomPromptTemplates()

        # System prompt for customer support context
        self.system_prompt = self.prompt_templates.get_system_prompt()
        
        logger.info(f"Initialized Gemini AI service with model: {model_name}")
    
    def generate_response(self, query: str, context: List[Dict[str, Any]],
                         conversation_history: Optional[List[Dict[str, str]]] = None,
                         intent: Optional[Dict[str, Any]] = None,
                         customer_info: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate response using query, context, conversation history, and intent

        Args:
            query: User's question or query
            context: Retrieved context from vector database
            conversation_history: Previous conversation messages
            intent: Classified intent information
            customer_info: Customer information if available

        Returns:
            Generated response string
        """
        try:
            # Build the prompt using advanced templates
            prompt = self.prompt_templates.build_main_prompt(
                query=query,
                context=context,
                conversation_history=conversation_history,
                intent=intent,
                customer_info=customer_info
            )

            # Generate response
            response = self.model.generate_content(prompt)
            
            if response.text:
                logger.info(f"Generated response for query: {query[:50]}...")
                return response.text.strip()
            else:
                logger.warning("Empty response from Gemini AI")
                return "I apologize, but I'm having trouble generating a response right now. Please contact our customer service team for immediate assistance."
                
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")

            # For quota errors, re-raise so the main pipeline can use fallback responses
            error_str = str(e).lower()
            if "quota" in error_str or "429" in str(e):
                raise e  # Re-raise quota errors to trigger fallback system

            # Handle other errors with generic responses
            if "api key" in error_str or "authentication" in error_str:
                return "I'm experiencing authentication issues. Please contact our customer service team at 1-800-TELECON for immediate assistance."
            elif "network" in error_str or "connection" in error_str:
                return "I'm experiencing network connectivity issues. Please try again in a moment or contact our customer service team at 1-800-TELECON for immediate assistance."
            else:
                return "I'm experiencing technical difficulties. Please contact our customer service team at 1-800-TELECON for immediate assistance."
    
    def _build_prompt(self, query: str, context: List[Dict[str, Any]], 
                     conversation_history: Optional[List[Dict[str, str]]] = None) -> str:
        """
        Build the complete prompt for Gemini AI
        
        Args:
            query: User's question
            context: Retrieved context documents
            conversation_history: Previous conversation
            
        Returns:
            Complete prompt string
        """
        prompt_parts = [self.system_prompt]
        
        # Add conversation history if available
        if conversation_history:
            prompt_parts.append("\n--- CONVERSATION HISTORY ---")
            for msg in conversation_history[-5:]:  # Last 5 messages
                role = msg.get("role", "user")
                content = msg.get("content", "")
                prompt_parts.append(f"{role.upper()}: {content}")
        
        # Add context from knowledge base
        if context:
            prompt_parts.append("\n--- RELEVANT INFORMATION FROM KNOWLEDGE BASE ---")
            for i, ctx in enumerate(context[:5]):  # Top 5 most relevant
                section = ctx.get("metadata", {}).get("section", "General")
                document = ctx.get("document", "")
                prompt_parts.append(f"\nContext {i+1} (Section: {section}):")
                prompt_parts.append(document)
        
        # Add current query
        prompt_parts.append(f"\n--- CURRENT CUSTOMER QUERY ---")
        prompt_parts.append(f"Customer: {query}")
        
        # Add response instruction
        prompt_parts.append(f"\n--- RESPONSE INSTRUCTION ---")
        prompt_parts.append("Based on the above context and conversation history, provide a helpful response to the customer's query. If the context doesn't contain relevant information, use your general knowledge about telecommunications but clearly indicate when you're doing so.")
        
        return "\n".join(prompt_parts)
    
    def generate_followup_questions(self, query: str, response: str, intent: Optional[Dict[str, Any]] = None) -> List[str]:
        """
        Generate relevant follow-up questions based on the query and response

        Args:
            query: Original user query
            response: Generated response
            intent: Intent classification information

        Returns:
            List of follow-up questions
        """
        try:
            # Use advanced prompt template for follow-up questions
            followup_prompt = self.prompt_templates.build_followup_questions_prompt(
                query=query,
                response=response,
                intent=intent or {}
            )

            response = self.model.generate_content(followup_prompt)
            
            if response.text:
                # Try to parse JSON response first
                try:
                    questions = json.loads(response.text.strip())
                    if isinstance(questions, list):
                        return questions[:3]  # Ensure max 3 questions
                except json.JSONDecodeError:
                    pass

                # Fallback: Parse the response to extract questions
                lines = response.text.strip().split('\n')
                questions = []
                for line in lines:
                    line = line.strip()
                    if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.')):
                        question = line[2:].strip()  # Remove number and dot
                        if question:
                            questions.append(question)

                return questions[:3]  # Ensure max 3 questions
            
        except Exception as e:
            logger.error(f"Error generating follow-up questions: {str(e)}")
        
        # Return default follow-up questions if generation fails
        return [
            "Is there anything else I can help you with?",
            "Do you need help with any other services?",
            "Would you like me to explain anything in more detail?"
        ]
    
    def classify_query_intent(self, query: str) -> Dict[str, Any]:
        """
        Classify the intent of the user query
        
        Args:
            query: User query
            
        Returns:
            Dictionary with intent classification
        """
        try:
            # Use advanced prompt template for intent classification
            classification_prompt = self.prompt_templates.build_intent_classification_prompt(query)

            response = self.model.generate_content(classification_prompt)
            
            if response.text:
                # Try to parse JSON response
                try:
                    result = json.loads(response.text.strip())
                    return result
                except json.JSONDecodeError:
                    # Fallback parsing
                    text = response.text.lower()
                    if any(word in text for word in ['technical', 'internet', 'connection', 'signal']):
                        category = 'technical_support'
                    elif any(word in text for word in ['bill', 'charge', 'payment', 'cost']):
                        category = 'billing_inquiry'
                    elif any(word in text for word in ['plan', 'change', 'upgrade', 'cancel']):
                        category = 'service_change'
                    elif any(word in text for word in ['complaint', 'problem', 'issue', 'angry']):
                        category = 'complaint'
                    elif any(word in text for word in ['emergency', 'urgent', 'critical']):
                        category = 'emergency'
                    else:
                        category = 'general_info'
                    
                    return {
                        "category": category,
                        "confidence": 0.7,
                        "urgency": "high" if category == "emergency" else "medium",
                        "requires_human": category in ["emergency", "complaint"]
                    }
            
        except Exception as e:
            logger.error(f"Error classifying query intent: {str(e)}")
        
        # Default classification
        return {
            "category": "general_info",
            "confidence": 0.5,
            "urgency": "medium",
            "requires_human": False
        }
    
    def test_connection(self) -> bool:
        """
        Test the Gemini AI API connection
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            test_response = self.model.generate_content("Hello, this is a test.")
            logger.info("Gemini AI API connection test successful")
            return bool(test_response.text)
        except Exception as e:
            logger.error(f"Gemini AI API connection test failed: {str(e)}")
            return False

# Example usage and testing
if __name__ == "__main__":
    try:
        # Initialize Gemini LLM service
        gemini_llm = TelecomGeminiLLM()
        
        # Test connection
        if not gemini_llm.test_connection():
            print("Failed to connect to Gemini AI API. Please check your API key.")
            exit(1)
        
        print(f"Gemini AI service initialized successfully")
        print(f"Model: {gemini_llm.model_name}")
        
        # Test query classification
        test_query = "My internet is very slow, how can I fix it?"
        classification = gemini_llm.classify_query_intent(test_query)
        print(f"\nQuery classification for: '{test_query}'")
        print(f"Category: {classification.get('category')}")
        print(f"Urgency: {classification.get('urgency')}")
        print(f"Requires human: {classification.get('requires_human')}")
        
        # Test response generation with mock context
        mock_context = [
            {
                "document": "To fix slow internet: 1. Restart your modem and router 2. Check for background downloads 3. Run speed test",
                "metadata": {"section": "Internet Issues"}
            }
        ]
        
        response = gemini_llm.generate_response(test_query, mock_context)
        print(f"\nGenerated response:")
        print(response)
        
        # Test follow-up questions
        followup_questions = gemini_llm.generate_followup_questions(test_query, response)
        print(f"\nSuggested follow-up questions:")
        for i, question in enumerate(followup_questions, 1):
            print(f"{i}. {question}")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        print("Make sure to set your GOOGLE_API_KEY in the .env file")
