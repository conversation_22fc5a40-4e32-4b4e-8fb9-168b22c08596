"""
Reset ChromaDB database to fix dimension mismatch
"""

import os
import shutil
from pathlib import Path

def reset_chromadb():
    """Reset the ChromaDB database"""
    print("🔄 Resetting ChromaDB database...")

    try:
        from vector_database import TelecomVectorDatabase

        # Try to reset the collection programmatically first
        vector_db = TelecomVectorDatabase()
        vector_db.reset_collection()
        print("✅ Reset ChromaDB collection programmatically")
        return True

    except Exception as e:
        print(f"⚠️ Programmatic reset failed: {str(e)}")
        print("Trying to remove directory...")

        # Fallback: Remove the existing ChromaDB directory
        chroma_path = Path("./chroma_db")
        if chroma_path.exists():
            try:
                shutil.rmtree(chroma_path)
                print("✅ Removed existing ChromaDB data")
            except Exception as e2:
                print(f"❌ Error removing ChromaDB data: {str(e2)}")
                print("💡 Please close any running applications and try again")
                return False

        # Create new empty directory
        chroma_path.mkdir(exist_ok=True)
        print("✅ Created new ChromaDB directory")

        return True

def test_new_database():
    """Test the new database with Gemini embeddings"""
    print("\n🧪 Testing new database...")
    
    try:
        from vector_database import TelecomVectorDatabase
        from embeddings import TelecomEmbeddingService
        from document_loader import TelecomDocumentLoader
        
        # Initialize components
        vector_db = TelecomVectorDatabase()
        embedding_service = TelecomEmbeddingService()
        document_loader = TelecomDocumentLoader()
        
        print("✅ Components initialized")
        
        # Load a small test document
        test_docs = document_loader.load_and_process_documents(["telecom_support_data.txt"])
        if not test_docs:
            print("❌ No documents found")
            return False
        
        print(f"✅ Loaded {len(test_docs)} document chunks")
        
        # Generate embeddings for first few documents
        test_docs_small = test_docs[:3]  # Just test with 3 documents
        embeddings = embedding_service.embed_documents(test_docs_small)
        
        print(f"✅ Generated {len(embeddings)} embeddings")
        print(f"✅ Embedding dimension: {len(embeddings[0]) if embeddings else 'N/A'}")
        
        # Add to database
        vector_db.add_documents(test_docs_small, embeddings)
        print("✅ Added documents to database")
        
        # Test search
        query_embedding = embedding_service.embed_query("internet connection")
        results = vector_db.similarity_search(query_embedding, k=2)
        
        if results:
            print(f"✅ Search successful - found {len(results)} results")
            return True
        else:
            print("❌ Search returned no results")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def main():
    """Main function"""
    print("🔧 ChromaDB Database Reset Tool")
    print("=" * 40)
    
    # Reset database
    if not reset_chromadb():
        print("❌ Failed to reset database")
        return False
    
    # Test new database
    if not test_new_database():
        print("❌ New database test failed")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 Database reset successful!")
    print("\nThe ChromaDB database has been reset and is now compatible")
    print("with Gemini embeddings (768 dimensions).")
    print("\nYou can now run your application:")
    print("python -m streamlit run main.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Database reset failed. Please check the errors above.")
        exit(1)
