#!/usr/bin/env python3
"""
Simple test script to check if google.generativeai can be imported
"""

try:
    print("Testing google.generativeai import...")
    import google.generativeai as genai
    print("✓ google.generativeai imported successfully")
    
    print("Testing other imports...")
    import streamlit as st
    print("✓ streamlit imported successfully")
    
    import chromadb
    print("✓ chromadb imported successfully")
    
    import langchain
    print("✓ langchain imported successfully")
    
    print("All imports successful!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")
