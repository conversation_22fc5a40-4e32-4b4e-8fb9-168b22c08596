#!/usr/bin/env python3
"""
Voice Demo - Complete Voice Input and Output Demo
"""

import streamlit as st
import logging
from voice_output import VoiceOutputHandler
from streamlit_voice import get_voice_input, test_microphone_access
from enhanced_voice import create_voice_conversation_widget

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

st.set_page_config(
    page_title="🎤🔊 Voice Demo",
    page_icon="🎤",
    layout="wide"
)

st.title("🎤🔊 Complete Voice Input & Output Demo")
st.markdown("---")

# Initialize voice handler
if 'voice_handler' not in st.session_state:
    st.session_state.voice_handler = VoiceOutputHandler()

# Voice status
col1, col2 = st.columns(2)

with col1:
    st.subheader("🎤 Voice Input Status")
    mic_status = test_microphone_access()
    if mic_status:
        st.success("✅ Microphone Available")
    else:
        st.error("❌ Microphone Not Available")

with col2:
    st.subheader("🔊 Voice Output Status")
    tts_status = st.session_state.voice_handler.is_tts_available()
    if tts_status:
        st.success("✅ Text-to-Speech Available")
    else:
        st.error("❌ Text-to-Speech Not Available")

st.markdown("---")

# Voice Input Demo
st.subheader("🎤 Voice Input Demo")
st.info("Click one of the buttons below to record your voice:")

input_col1, input_col2, input_col3 = st.columns(3)

with input_col1:
    st.markdown("**Quick Voice Input**")
    voice_text = get_voice_input()

with input_col2:
    st.markdown("**Voice Conversation**")
    conversation_text = create_voice_conversation_widget()

with input_col3:
    st.markdown("**Manual Text Input**")
    manual_text = st.text_input("Type here:", key="manual_input")

# Determine which input was used
user_input = None
input_method = ""

if voice_text:
    user_input = voice_text
    input_method = "🎤 Quick Voice"
elif conversation_text:
    user_input = conversation_text
    input_method = "🎭 Voice Conversation"
elif manual_text:
    user_input = manual_text
    input_method = "⌨️ Manual Text"

# Display input result
if user_input:
    st.markdown("---")
    st.subheader("📝 Your Input")
    st.success(f"**Method:** {input_method}")
    st.write(f"**Text:** {user_input}")
    
    # Voice Output Demo
    st.markdown("---")
    st.subheader("🔊 Voice Output Demo")
    
    # Create a response
    response = f"You said: {user_input}. This is the bot's response to your input."
    
    st.write(f"**Bot Response:** {response}")
    
    # Voice output options
    output_col1, output_col2, output_col3 = st.columns(3)
    
    with output_col1:
        if st.button("🔊 Speak Response", help="Play the bot response"):
            with st.spinner("🔊 Speaking..."):
                success = st.session_state.voice_handler.speak_text(response)
                if success:
                    st.success("✅ Voice played!")
                else:
                    st.error("❌ Voice failed!")
    
    with output_col2:
        if st.button("🔊 Speak Your Input", help="Play back what you said"):
            with st.spinner("🔊 Speaking..."):
                success = st.session_state.voice_handler.speak_text(f"You said: {user_input}")
                if success:
                    st.success("✅ Voice played!")
                else:
                    st.error("❌ Voice failed!")
    
    with output_col3:
        # Auto-play toggle
        auto_play = st.checkbox("🔄 Auto-play responses", value=False)
        if auto_play:
            st.info("🔊 Auto-playing response...")
            success = st.session_state.voice_handler.speak_text(response)
            if success:
                st.success("✅ Auto-play successful!")

# Voice Settings
st.markdown("---")
st.subheader("🎛️ Voice Settings")

settings_col1, settings_col2 = st.columns(2)

with settings_col1:
    if tts_status:
        rate = st.slider("Speech Rate (WPM)", 100, 300, 180)
        volume = st.slider("Volume", 0.0, 1.0, 0.9, 0.1)
        
        if st.button("Apply Settings"):
            st.session_state.voice_handler.set_voice_settings(rate=rate, volume=volume)
            st.success("Settings applied!")

with settings_col2:
    if tts_status:
        voices = st.session_state.voice_handler.get_available_voices()
        if voices:
            voice_names = [voice['name'] for voice in voices]
            selected_voice = st.selectbox("Select Voice", voice_names)
            
            if st.button("Change Voice"):
                for voice in voices:
                    if voice['name'] == selected_voice:
                        st.session_state.voice_handler.set_voice_settings(voice_id=voice['id'])
                        st.success(f"Voice changed to: {selected_voice}")
                        break

# Instructions
st.markdown("---")
st.subheader("📋 How to Use")

st.markdown("""
### Voice Input Options:
1. **🎤 Quick Voice**: Simple voice recording (8 seconds)
2. **🎭 Voice Conversation**: Enhanced voice input with auto-enable voice output
3. **⌨️ Manual Text**: Traditional text input

### Voice Output Features:
- **Manual Play**: Click 🔊 buttons to hear responses
- **Auto-play**: Enable checkbox for automatic voice responses
- **Voice Settings**: Adjust speed, volume, and voice selection

### Tips for Best Results:
- **Speak clearly** and at normal pace
- **Minimize background noise**
- **Allow microphone permissions** in your browser
- **Use headphones** to prevent audio feedback
""")

# Debug Information
with st.expander("🔧 Debug Information"):
    st.write("**Session State:**")
    st.json({
        "voice_handler_initialized": 'voice_handler' in st.session_state,
        "tts_available": tts_status,
        "microphone_available": mic_status,
        "last_input": user_input if user_input else "None",
        "input_method": input_method if input_method else "None"
    })
