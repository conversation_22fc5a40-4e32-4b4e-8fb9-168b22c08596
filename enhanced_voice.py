#!/usr/bin/env python3
"""
Enhanced Voice Features for TeleConnect RAG Bot
Combines voice input and output for complete voice conversation
"""

import streamlit as st
import speech_recognition as sr
import pyttsx3
import threading
import time
from typing import Optional, Tuple, Dict, Any
import logging
import tempfile
import os

logger = logging.getLogger(__name__)

class EnhancedVoiceHandler:
    """
    Enhanced voice handler combining input and output capabilities
    """
    
    def __init__(self):
        """Initialize enhanced voice handler"""
        # Voice input components
        self.recognizer = sr.Recognizer()
        self.microphone = None
        
        # Voice output components
        self.tts_engine = None
        
        # State management
        self.is_listening = False
        self.is_speaking = False
        self.continuous_mode = False
        
        # Initialize components
        self._initialize_voice_input()
        self._initialize_voice_output()
    
    def _initialize_voice_input(self):
        """Initialize voice input components"""
        try:
            self.microphone = sr.Microphone()
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            logger.info("Voice input initialized successfully")
        except Exception as e:
            logger.error(f"Voice input initialization failed: {e}")
            self.microphone = None
    
    def _initialize_voice_output(self):
        """Initialize voice output components"""
        try:
            self.tts_engine = pyttsx3.init()
            
            # Configure voice settings
            self.tts_engine.setProperty('rate', 180)
            self.tts_engine.setProperty('volume', 0.9)
            
            # Select best voice
            voices = self.tts_engine.getProperty('voices')
            if voices:
                for voice in voices:
                    if 'female' in voice.name.lower() or 'zira' in voice.name.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            
            logger.info("Voice output initialized successfully")
        except Exception as e:
            logger.error(f"Voice output initialization failed: {e}")
            self.tts_engine = None
    
    def is_voice_available(self) -> Dict[str, bool]:
        """Check availability of voice features"""
        return {
            "input": self.microphone is not None,
            "output": self.tts_engine is not None,
            "full_duplex": self.microphone is not None and self.tts_engine is not None
        }
    
    def listen_for_speech(self, timeout: int = 8) -> Tuple[Optional[str], Optional[str]]:
        """
        Listen for speech input
        
        Args:
            timeout: Maximum listening time in seconds
            
        Returns:
            Tuple of (transcribed_text, error_message)
        """
        if not self.microphone:
            return None, "Microphone not available"
        
        try:
            self.is_listening = True
            
            with self.microphone as source:
                logger.info(f"Listening for speech (timeout: {timeout}s)...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=timeout)
            
            self.is_listening = False
            
            # Transcribe audio
            text = self.recognizer.recognize_google(audio)
            logger.info(f"Speech recognized: {text}")
            
            return text, None
            
        except sr.UnknownValueError:
            self.is_listening = False
            return None, "Could not understand speech"
        except sr.RequestError as e:
            self.is_listening = False
            return None, f"Speech recognition service error: {e}"
        except sr.WaitTimeoutError:
            self.is_listening = False
            return None, "No speech detected"
        except Exception as e:
            self.is_listening = False
            return None, f"Voice input error: {str(e)}"
    
    def speak_text(self, text: str) -> bool:
        """
        Speak text aloud
        
        Args:
            text: Text to speak
            
        Returns:
            True if successful, False otherwise
        """
        if not self.tts_engine or not text.strip():
            return False
        
        try:
            self.is_speaking = True
            
            # Clean text for better speech
            clean_text = self._clean_text_for_speech(text)
            
            # Speak the text
            self.tts_engine.say(clean_text)
            self.tts_engine.runAndWait()
            
            self.is_speaking = False
            return True
            
        except Exception as e:
            logger.error(f"Text-to-speech error: {e}")
            self.is_speaking = False
            return False
    
    def speak_text_async(self, text: str) -> threading.Thread:
        """
        Speak text asynchronously
        
        Args:
            text: Text to speak
            
        Returns:
            Thread object
        """
        def speak_worker():
            self.speak_text(text)
        
        thread = threading.Thread(target=speak_worker, daemon=True)
        thread.start()
        return thread
    
    def _clean_text_for_speech(self, text: str) -> str:
        """Clean text for better speech synthesis"""
        # Remove markdown formatting
        clean_text = text.replace("**", "").replace("*", "")
        clean_text = clean_text.replace("###", "").replace("##", "").replace("#", "")
        
        # Replace abbreviations
        replacements = {
            "TeleConnect": "Tele Connect",
            "WiFi": "Wi-Fi",
            "1-800-TELECON": "1 800 TELECON",
            "1-800-TECH-HELP": "1 800 TECH HELP",
            "1-800-BILLING": "1 800 BILLING",
            "Mbps": "megabits per second",
            "GB": "gigabytes",
            "24/7": "24 hours a day, 7 days a week"
        }
        
        for abbrev, full_form in replacements.items():
            clean_text = clean_text.replace(abbrev, full_form)
        
        # Add natural pauses
        clean_text = clean_text.replace(". ", ". ... ")
        clean_text = clean_text.replace("! ", "! ... ")
        clean_text = clean_text.replace("? ", "? ... ")
        
        return clean_text.strip()
    
    def start_continuous_conversation(self):
        """Start continuous voice conversation mode"""
        self.continuous_mode = True
    
    def stop_continuous_conversation(self):
        """Stop continuous voice conversation mode"""
        self.continuous_mode = False
        self.is_listening = False
        self.is_speaking = False

def create_enhanced_voice_interface():
    """
    Create enhanced voice interface with both input and output
    
    Returns:
        Dictionary with voice interaction results
    """
    
    # Initialize enhanced voice handler
    if 'enhanced_voice_handler' not in st.session_state:
        st.session_state.enhanced_voice_handler = EnhancedVoiceHandler()
    
    voice_handler = st.session_state.enhanced_voice_handler
    
    # Check voice availability
    voice_status = voice_handler.is_voice_available()
    
    st.markdown("### 🎤🔊 Enhanced Voice Interface")
    
    # Voice status display
    col1, col2, col3 = st.columns(3)
    
    with col1:
        input_icon = "✅" if voice_status["input"] else "❌"
        st.markdown(f"**🎤 Voice Input:** {input_icon}")
    
    with col2:
        output_icon = "✅" if voice_status["output"] else "❌"
        st.markdown(f"**🔊 Voice Output:** {output_icon}")
    
    with col3:
        full_duplex_icon = "✅" if voice_status["full_duplex"] else "❌"
        st.markdown(f"**🎭 Full Conversation:** {full_duplex_icon}")
    
    # Voice interaction controls
    if voice_status["full_duplex"]:
        st.markdown("#### 🎭 Voice Conversation Mode")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🎤 Start Voice Chat", key="start_voice_chat"):
                st.session_state.voice_chat_active = True
        
        with col2:
            if st.button("⏹️ Stop Voice Chat", key="stop_voice_chat"):
                st.session_state.voice_chat_active = False
                voice_handler.stop_continuous_conversation()
        
        with col3:
            if st.button("🎤 Single Voice Input", key="single_voice_input"):
                return handle_single_voice_input(voice_handler)
        
        # Voice chat session
        if st.session_state.get('voice_chat_active', False):
            return handle_voice_chat_session(voice_handler)
    
    elif voice_status["input"]:
        # Voice input only
        st.markdown("#### 🎤 Voice Input Only")
        if st.button("🎤 Record Voice Input", key="voice_input_only"):
            return handle_single_voice_input(voice_handler)
    
    elif voice_status["output"]:
        # Voice output only
        st.markdown("#### 🔊 Voice Output Only")
        st.info("Voice input not available. You can still hear responses.")
    
    else:
        st.error("❌ Voice features not available. Please check your audio settings.")
    
    return None

def handle_single_voice_input(voice_handler: EnhancedVoiceHandler) -> Optional[str]:
    """Handle single voice input"""
    
    with st.spinner("🎤 Listening... Speak now!"):
        text, error = voice_handler.listen_for_speech(timeout=8)
    
    if text:
        st.success(f"✅ Voice input: {text}")
        return text
    else:
        st.error(f"❌ Voice input failed: {error}")
        return None

def handle_voice_chat_session(voice_handler: EnhancedVoiceHandler) -> Optional[str]:
    """Handle continuous voice chat session"""
    
    st.info("🎭 **Voice Chat Active** - Speak your question!")
    
    # Status indicators
    status_col1, status_col2 = st.columns(2)
    
    with status_col1:
        if voice_handler.is_listening:
            st.success("🎤 **LISTENING**")
        else:
            st.info("🎤 Ready to listen")
    
    with status_col2:
        if voice_handler.is_speaking:
            st.success("🔊 **SPEAKING**")
        else:
            st.info("🔊 Ready to speak")
    
    # Voice input
    if not voice_handler.is_speaking:
        with st.spinner("🎤 Listening for your question..."):
            text, error = voice_handler.listen_for_speech(timeout=10)
        
        if text:
            st.success(f"✅ You said: {text}")
            return text
        elif error and "No speech detected" not in error:
            st.error(f"❌ {error}")
    
    return None

def create_voice_conversation_widget():
    """
    Create a simplified voice conversation widget

    Returns:
        Voice input text or None
    """

    # Voice conversation button with better styling
    if st.button("🎭 Voice Chat", help="Start a voice conversation (speak and listen)"):
        if 'enhanced_voice_handler' not in st.session_state:
            st.session_state.enhanced_voice_handler = EnhancedVoiceHandler()

        voice_handler = st.session_state.enhanced_voice_handler
        voice_status = voice_handler.is_voice_available()

        if not voice_status["input"]:
            st.error("🚫 Voice input not available. Please check microphone permissions.")
            return None

        # Create a status placeholder for better feedback
        status = st.empty()
        status.info("🎤 Listening... Speak your question clearly!")

        # Get voice input
        text, error = voice_handler.listen_for_speech(timeout=8)

        if text:
            status.success(f"✅ Voice input: {text}")

            # Auto-enable voice output for conversation mode
            if not st.session_state.get('voice_output_enabled', False):
                st.session_state.voice_output_enabled = True
                st.session_state.voice_auto_play = True
                status.info("🔊 Voice output automatically enabled for conversation!")

            # Clear status after a moment
            import threading
            def clear_status():
                import time
                time.sleep(3)
                status.empty()
            threading.Thread(target=clear_status, daemon=True).start()

            return text
        elif error:
            status.error(f"❌ {error}")
            return None
        else:
            st.error(f"❌ Voice input failed: {error}")
            return None
    
    return None

# Test function
def test_enhanced_voice():
    """Test enhanced voice functionality"""
    
    print("=== Testing Enhanced Voice Features ===")
    
    voice_handler = EnhancedVoiceHandler()
    voice_status = voice_handler.is_voice_available()
    
    print(f"Voice Input: {'✅' if voice_status['input'] else '❌'}")
    print(f"Voice Output: {'✅' if voice_status['output'] else '❌'}")
    print(f"Full Duplex: {'✅' if voice_status['full_duplex'] else '❌'}")
    
    if voice_status["full_duplex"]:
        print("\n🎭 Full voice conversation available!")
        
        # Test voice output
        print("🔊 Testing voice output...")
        success = voice_handler.speak_text("Hello! This is a test of the enhanced voice system.")
        print(f"Voice output test: {'✅' if success else '❌'}")
        
        return True
    else:
        print("\n⚠️ Full voice conversation not available")
        return False

# Example usage
if __name__ == "__main__":
    test_enhanced_voice()
