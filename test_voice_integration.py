#!/usr/bin/env python3
"""
Test voice input integration with the RAG bot
"""

def test_voice_components():
    """Test individual voice input components"""
    
    print("=== Testing Voice Input Components ===\n")
    
    # Test 1: Import modules
    try:
        from streamlit_voice import get_voice_input, test_microphone_access, handle_voice_input
        print("✅ Voice modules imported successfully")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test 2: Speech recognition library
    try:
        import speech_recognition as sr
        print("✅ SpeechRecognition library available")
    except ImportError:
        print("❌ SpeechRecognition library not installed")
        return False
    
    # Test 3: PyAudio library
    try:
        import pyaudio
        print("✅ PyAudio library available")
    except ImportError:
        print("❌ PyAudio library not installed")
        return False
    
    # Test 4: Microphone access
    try:
        mic_available = test_microphone_access()
        if mic_available:
            print("✅ Microphone access available")
        else:
            print("⚠️  Microphone access not available (may work in browser)")
    except Exception as e:
        print(f"⚠️  Microphone test error: {e}")
    
    return True

def test_rag_integration():
    """Test RAG pipeline integration with voice input"""
    
    print("\n=== Testing RAG Integration ===\n")
    
    try:
        from rag_pipeline import TelecomRAGPipeline
        
        # Initialize RAG pipeline
        rag = TelecomRAGPipeline()
        print("✅ RAG pipeline initialized")
        
        # Test with simulated voice input
        test_voice_queries = [
            "My internet is very slow",
            "I need help with my bill", 
            "How do I upgrade my plan",
            "My phone has no signal"
        ]
        
        print("\n🧪 Testing voice queries through RAG pipeline:")
        
        for query in test_voice_queries:
            print(f"\n📝 Simulated voice input: '{query}'")
            
            try:
                result = rag.process_query(query)
                
                print(f"   ✅ Intent: {result['intent']['category']}")
                print(f"   ✅ Response length: {len(result['response'])} chars")
                print(f"   ✅ Response preview: {result['response'][:80]}...")
                
                if result.get('followup_questions'):
                    print(f"   ✅ Follow-up questions: {len(result['followup_questions'])}")
                
            except Exception as e:
                print(f"   ❌ Error processing query: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG integration test failed: {e}")
        return False

def test_streamlit_integration():
    """Test Streamlit app integration"""
    
    print("\n=== Testing Streamlit Integration ===\n")
    
    try:
        # Test main.py imports
        import sys
        import os
        
        # Add current directory to path
        sys.path.insert(0, os.getcwd())
        
        # Test imports from main.py
        from streamlit_voice import get_voice_input, test_microphone_access
        print("✅ Streamlit voice imports successful")
        
        # Test that main.py can be imported (syntax check)
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "main.py")
        if spec and spec.loader:
            print("✅ main.py syntax is valid")
        else:
            print("❌ main.py has syntax issues")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit integration test failed: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions for voice input"""
    
    print("\n=== Voice Input Usage Instructions ===\n")
    
    print("🎤 **How to use Voice Input in your app:**")
    print()
    print("1. **Start the app:**")
    print("   streamlit run main.py")
    print()
    print("2. **Open in browser:**")
    print("   http://localhost:8501")
    print()
    print("3. **Use voice input:**")
    print("   • Look for the 🎤 button next to the text input")
    print("   • Click the button to start recording")
    print("   • Allow microphone access when prompted")
    print("   • Speak clearly for up to 8 seconds")
    print("   • Watch your speech convert to text")
    print()
    print("4. **Check status:**")
    print("   • Sidebar shows microphone availability")
    print("   • Green ✅ means voice input is ready")
    print("   • Red ❌ means microphone needs setup")
    print()
    print("💡 **Tips for best results:**")
    print("   • Speak at normal pace")
    print("   • Minimize background noise")
    print("   • Use complete sentences")
    print("   • Ensure stable internet connection")
    print()
    print("🔧 **Troubleshooting:**")
    print("   • If microphone doesn't work, check browser permissions")
    print("   • Try refreshing the page and allowing access again")
    print("   • Make sure you're using HTTPS (required for microphone)")
    print("   • Test microphone in other apps to verify it works")

def main():
    """Run all voice input tests"""
    
    print("🎤 Voice Input Integration Test Suite")
    print("=" * 50)
    
    # Run tests
    tests = [
        ("Voice Components", test_voice_components),
        ("RAG Integration", test_rag_integration),
        ("Streamlit Integration", test_streamlit_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n❌ {test_name}: ERROR - {e}")
    
    # Show results
    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Voice input is ready to use.")
        show_usage_instructions()
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the errors above.")
        print("\nCommon issues:")
        print("• Make sure all dependencies are installed: pip install -r requirements.txt")
        print("• Check microphone permissions in your browser")
        print("• Ensure you're running on HTTPS for microphone access")

if __name__ == "__main__":
    main()
