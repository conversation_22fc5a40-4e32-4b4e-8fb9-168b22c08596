#!/usr/bin/env python3
"""
Test RAG pipeline functionality
"""

import sys
import logging
from rag_pipeline import TelecomRAGPipeline

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_rag_pipeline():
    """Test RAG pipeline initialization and query processing"""
    
    print("=== Testing RAG Pipeline ===")
    
    try:
        print("1. Initializing RAG pipeline...")
        rag = TelecomRAGPipeline()
        print("✅ RAG pipeline initialized successfully!")
        
        print("\n2. Testing simple query...")
        test_query = "Hello, what services do you offer?"
        print(f"Query: {test_query}")
        
        result = rag.process_query(test_query)
        
        print(f"\n3. Result analysis:")
        print(f"   Type: {type(result)}")
        print(f"   Keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            response = result.get("response", "No response found")
            print(f"   Response: {response[:200]}...")
            
            intent = result.get("intent", {})
            print(f"   Intent: {intent}")
            
            sources = result.get("context_sources", [])
            print(f"   Sources: {len(sources)} found")
            
            followup = result.get("followup_questions", [])
            print(f"   Follow-up questions: {len(followup)} found")
            
            return True
        else:
            print("❌ Result is not a dictionary")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("RAG pipeline test failed")
        return False

if __name__ == "__main__":
    success = test_rag_pipeline()
    if success:
        print("\n🎉 RAG pipeline test passed!")
        sys.exit(0)
    else:
        print("\n💥 RAG pipeline test failed!")
        sys.exit(1)
