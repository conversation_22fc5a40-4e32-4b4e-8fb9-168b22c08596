#!/usr/bin/env python3
"""
Test Google API key and Gemini AI functionality
"""

import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_api_key():
    """Test if the API key works"""
    try:
        api_key = os.getenv("GOOGLE_API_KEY")
        print(f"API Key found: {api_key[:10]}...{api_key[-5:] if api_key else 'None'}")
        
        if not api_key:
            print("❌ No API key found in environment")
            return False
            
        # Configure Gemini AI
        genai.configure(api_key=api_key)
        
        # Test with a simple model
        model = genai.GenerativeModel("gemini-1.5-flash")
        print("✓ Model initialized successfully")
        
        # Test generation
        response = model.generate_content("Say hello")
        print(f"✓ Response received: {response.text[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing API: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== Testing Google API Key ===")
    success = test_api_key()
    print(f"\nAPI Test: {'✓ PASSED' if success else '❌ FAILED'}")
