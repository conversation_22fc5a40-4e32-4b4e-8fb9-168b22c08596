"""
Quick test to verify the RAG system is working with Gemini only
"""

from rag_pipeline import TelecomRAGPipeline

def test_rag_system():
    """Test the complete RAG system"""
    print("🧪 Testing TeleConnect RAG System")
    print("=" * 40)
    
    try:
        # Initialize RAG pipeline
        print("Initializing RAG pipeline...")
        rag = TelecomRAGPipeline()
        print("✅ RAG pipeline initialized")
        
        # Test system stats
        stats = rag.get_system_stats()
        print(f"✅ Vector DB documents: {stats.get('vector_database', {}).get('total_documents', 0)}")
        print(f"✅ Gemini Embeddings: {'Working' if stats.get('api_status', {}).get('gemini_embeddings') else 'Failed'}")
        print(f"✅ Gemini LLM: {'Working' if stats.get('api_status', {}).get('gemini_llm') else 'Failed'}")
        
        # Test queries
        test_queries = [
            "My internet is very slow, what can I do?",
            "How do I change my mobile plan?",
            "What are the charges on my bill?",
            "I need help with my phone signal"
        ]
        
        print(f"\n🔍 Testing {len(test_queries)} queries...")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\nQuery {i}: {query}")
            
            result = rag.process_query(query)
            
            if result and result.get("response"):
                print(f"✅ Response generated ({len(result['response'])} chars)")
                print(f"   Intent: {result.get('intent', {}).get('category', 'unknown')}")
                print(f"   Context sources: {len(result.get('context_sources', []))}")
                print(f"   Follow-up questions: {len(result.get('followup_questions', []))}")
            else:
                print("❌ No response generated")
                return False
        
        print(f"\n🎉 All tests passed!")
        print(f"\nYour RAG system is working perfectly with:")
        print(f"✅ Gemini AI for embeddings (768 dimensions)")
        print(f"✅ Gemini AI Flash 1.5 for LLM responses")
        print(f"✅ ChromaDB for vector storage")
        print(f"✅ Complete RAG pipeline")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_rag_system()
    if success:
        print(f"\n🚀 Your application is ready!")
        print(f"Run: python -m streamlit run main.py")
        print(f"Open: http://localhost:8501")
    else:
        print(f"\n❌ System test failed. Please check the errors above.")
