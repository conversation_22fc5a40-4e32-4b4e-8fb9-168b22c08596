#!/usr/bin/env python3
"""
Voice Input Module for TeleConnect Customer Support RAG Bot
Provides speech-to-text functionality for voice queries
"""

import streamlit as st
import speech_recognition as sr
import pyaudio
import wave
import tempfile
import os
import threading
import time
from typing import Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceInputHandler:
    """
    Handles voice input functionality for the RAG bot
    """
    
    def __init__(self):
        """Initialize voice input handler"""
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.is_recording = False
        self.audio_data = None
        
        # Audio settings
        self.sample_rate = 16000
        self.chunk_size = 1024
        self.audio_format = pyaudio.paInt16
        self.channels = 1
        
        # Initialize microphone
        self._initialize_microphone()
    
    def _initialize_microphone(self):
        """Initialize microphone with error handling"""
        try:
            self.microphone = sr.Microphone()
            # Adjust for ambient noise
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
            logger.info("Microphone initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize microphone: {e}")
            self.microphone = None
    
    def is_microphone_available(self) -> bool:
        """Check if microphone is available"""
        return self.microphone is not None
    
    def record_audio(self, duration: int = 10) -> Optional[sr.AudioData]:
        """
        Record audio from microphone
        
        Args:
            duration: Maximum recording duration in seconds
            
        Returns:
            AudioData object or None if failed
        """
        if not self.microphone:
            return None
        
        try:
            with self.microphone as source:
                logger.info(f"Recording audio for up to {duration} seconds...")
                # Listen for audio with timeout
                audio_data = self.recognizer.listen(
                    source, 
                    timeout=duration,
                    phrase_time_limit=duration
                )
                logger.info("Audio recording completed")
                return audio_data
        except sr.WaitTimeoutError:
            logger.warning("Recording timeout - no speech detected")
            return None
        except Exception as e:
            logger.error(f"Error recording audio: {e}")
            return None
    
    def transcribe_audio(self, audio_data: sr.AudioData) -> Tuple[Optional[str], Optional[str]]:
        """
        Transcribe audio to text using multiple recognition services
        
        Args:
            audio_data: AudioData object to transcribe
            
        Returns:
            Tuple of (transcribed_text, error_message)
        """
        if not audio_data:
            return None, "No audio data provided"
        
        # Try multiple recognition services in order of preference
        recognition_methods = [
            ("Google Web Speech API", self._transcribe_with_google),
            ("Google Cloud Speech", self._transcribe_with_google_cloud),
            ("Whisper (OpenAI)", self._transcribe_with_whisper),
            ("Sphinx (Offline)", self._transcribe_with_sphinx)
        ]
        
        for method_name, method_func in recognition_methods:
            try:
                logger.info(f"Trying transcription with {method_name}...")
                text = method_func(audio_data)
                if text and text.strip():
                    logger.info(f"Successfully transcribed with {method_name}")
                    return text.strip(), None
            except Exception as e:
                logger.warning(f"{method_name} failed: {e}")
                continue
        
        return None, "All transcription methods failed. Please try again or type your question."
    
    def _transcribe_with_google(self, audio_data: sr.AudioData) -> Optional[str]:
        """Transcribe using Google Web Speech API (free)"""
        return self.recognizer.recognize_google(audio_data)
    
    def _transcribe_with_google_cloud(self, audio_data: sr.AudioData) -> Optional[str]:
        """Transcribe using Google Cloud Speech API"""
        # This requires Google Cloud credentials
        return self.recognizer.recognize_google_cloud(audio_data)
    
    def _transcribe_with_whisper(self, audio_data: sr.AudioData) -> Optional[str]:
        """Transcribe using OpenAI Whisper"""
        # This requires openai-whisper package
        return self.recognizer.recognize_whisper(audio_data)
    
    def _transcribe_with_sphinx(self, audio_data: sr.AudioData) -> Optional[str]:
        """Transcribe using CMU Sphinx (offline)"""
        return self.recognizer.recognize_sphinx(audio_data)
    
    def record_and_transcribe(self, duration: int = 10) -> Tuple[Optional[str], Optional[str]]:
        """
        Record audio and transcribe to text in one step
        
        Args:
            duration: Maximum recording duration in seconds
            
        Returns:
            Tuple of (transcribed_text, error_message)
        """
        # Record audio
        audio_data = self.record_audio(duration)
        if not audio_data:
            return None, "Failed to record audio. Please check your microphone."
        
        # Transcribe audio
        return self.transcribe_audio(audio_data)

def create_voice_input_ui():
    """
    Create voice input UI components for Streamlit
    
    Returns:
        Transcribed text if voice input was used, None otherwise
    """
    
    # Initialize voice handler in session state
    if 'voice_handler' not in st.session_state:
        st.session_state.voice_handler = VoiceInputHandler()
    
    voice_handler = st.session_state.voice_handler
    
    # Voice input section
    st.markdown("### 🎤 Voice Input")
    
    if not voice_handler.is_microphone_available():
        st.error("🚫 Microphone not available. Please check your microphone settings.")
        st.info("💡 Make sure your browser has microphone permissions enabled.")
        return None
    
    # Voice input controls
    col1, col2, col3 = st.columns([1, 1, 2])
    
    with col1:
        if st.button("🎤 Start Recording", key="start_recording"):
            st.session_state.recording_state = "recording"
    
    with col2:
        if st.button("⏹️ Stop & Transcribe", key="stop_recording"):
            st.session_state.recording_state = "processing"
    
    with col3:
        recording_duration = st.slider("Recording Duration (seconds)", 3, 30, 10)
    
    # Recording status
    if 'recording_state' not in st.session_state:
        st.session_state.recording_state = "idle"
    
    if st.session_state.recording_state == "recording":
        with st.spinner("🎤 Recording... Speak now!"):
            # Record audio
            audio_data = voice_handler.record_audio(recording_duration)
            
            if audio_data:
                st.session_state.recorded_audio = audio_data
                st.session_state.recording_state = "recorded"
                st.success("✅ Recording completed! Click 'Stop & Transcribe' to convert to text.")
            else:
                st.error("❌ Recording failed. Please try again.")
                st.session_state.recording_state = "idle"
    
    elif st.session_state.recording_state == "processing":
        if 'recorded_audio' in st.session_state:
            with st.spinner("🔄 Converting speech to text..."):
                text, error = voice_handler.transcribe_audio(st.session_state.recorded_audio)
                
                if text:
                    st.success(f"✅ Transcription successful!")
                    st.info(f"**You said:** {text}")
                    st.session_state.recording_state = "idle"
                    return text
                else:
                    st.error(f"❌ Transcription failed: {error}")
                    st.session_state.recording_state = "idle"
        else:
            st.error("❌ No audio recorded. Please record audio first.")
            st.session_state.recording_state = "idle"
    
    elif st.session_state.recording_state == "recorded":
        st.info("🎵 Audio recorded. Click 'Stop & Transcribe' to convert to text.")
    
    # Voice input tips
    with st.expander("💡 Voice Input Tips"):
        st.markdown("""
        **For best results:**
        - Speak clearly and at a normal pace
        - Minimize background noise
        - Keep your microphone close (but not too close)
        - Speak in complete sentences
        - Wait for the recording to start before speaking
        
        **Supported languages:** English (primary), with basic support for other languages
        
        **Privacy:** Voice data is processed locally and not stored permanently
        """)
    
    return None

def create_voice_input_widget():
    """
    Create a simplified voice input widget

    Returns:
        Transcribed text or None
    """

    # Initialize voice handler in session state
    if 'voice_handler' not in st.session_state:
        st.session_state.voice_handler = VoiceInputHandler()

    voice_handler = st.session_state.voice_handler

    # Voice input button
    if st.button("🎤 Voice", help="Click to record your question", key="voice_input_btn"):
        if not voice_handler.is_microphone_available():
            st.error("🚫 Microphone not available")
            return None

        # Show recording status
        with st.spinner("🎤 Recording for 8 seconds... Speak now!"):
            try:
                text, error = voice_handler.record_and_transcribe(duration=8)

                if text:
                    st.success(f"✅ Voice: {text}")
                    # Store in session state to persist across reruns
                    st.session_state.voice_input_text = text
                    return text
                else:
                    st.error(f"❌ Voice failed: {error}")
                    return None
            except Exception as e:
                st.error(f"❌ Voice error: {str(e)}")
                return None

    # Return stored voice input if available
    if hasattr(st.session_state, 'voice_input_text'):
        text = st.session_state.voice_input_text
        delattr(st.session_state, 'voice_input_text')  # Clear after use
        return text

    return None

# Audio processing utilities
def save_audio_to_file(audio_data: sr.AudioData, filename: str = None) -> str:
    """
    Save audio data to a temporary file
    
    Args:
        audio_data: AudioData object
        filename: Optional filename, will generate temp file if not provided
        
    Returns:
        Path to saved audio file
    """
    if not filename:
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".wav")
        filename = temp_file.name
        temp_file.close()
    
    with open(filename, "wb") as f:
        f.write(audio_data.get_wav_data())
    
    return filename

def test_microphone():
    """Test microphone functionality"""
    voice_handler = VoiceInputHandler()
    
    if not voice_handler.is_microphone_available():
        print("❌ Microphone not available")
        return False
    
    print("🎤 Testing microphone... Say something!")
    text, error = voice_handler.record_and_transcribe(duration=5)
    
    if text:
        print(f"✅ Success! Transcribed: {text}")
        return True
    else:
        print(f"❌ Failed: {error}")
        return False

# Example usage
if __name__ == "__main__":
    print("=== Voice Input Module Test ===")
    test_microphone()
