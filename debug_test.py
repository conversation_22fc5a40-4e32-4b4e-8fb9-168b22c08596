#!/usr/bin/env python3
"""
Debug script to test the RAG pipeline components
"""

import traceback
import sys

def test_gemini_llm():
    """Test Gemini LLM component"""
    try:
        print("Testing Gemini LLM...")
        from gemini_llm import TelecomGeminiLLM
        
        llm = TelecomGeminiLLM()
        print("✓ LLM initialized successfully")
        
        # Test simple response generation
        result = llm.generate_response("Hello, test query", [])
        print(f"✓ Response generated: {result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in Gemini LLM: {str(e)}")
        traceback.print_exc()
        return False

def test_embeddings():
    """Test embedding service"""
    try:
        print("\nTesting Embeddings...")
        from embeddings import TelecomEmbeddingService
        
        embedding_service = TelecomEmbeddingService()
        print("✓ Embedding service initialized successfully")
        
        # Test embedding generation
        embeddings = embedding_service.generate_embeddings(["test text"])
        print(f"✓ Embeddings generated: {len(embeddings)} embeddings")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in Embeddings: {str(e)}")
        traceback.print_exc()
        return False

def test_full_pipeline():
    """Test full RAG pipeline"""
    try:
        print("\nTesting Full Pipeline...")
        from rag_pipeline import TelecomRAGPipeline
        
        rag = TelecomRAGPipeline()
        print("✓ RAG pipeline initialized successfully")
        
        # Test query processing
        result = rag.process_query("My internet is slow")
        print(f"✓ Query processed successfully")
        print(f"Response: {result['response'][:200]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in RAG Pipeline: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== RAG Pipeline Debug Test ===\n")
    
    # Test individual components
    gemini_ok = test_gemini_llm()
    embeddings_ok = test_embeddings()
    
    if gemini_ok and embeddings_ok:
        pipeline_ok = test_full_pipeline()
    else:
        print("\nSkipping full pipeline test due to component failures")
        pipeline_ok = False
    
    print(f"\n=== Test Results ===")
    print(f"Gemini LLM: {'✓' if gemini_ok else '✗'}")
    print(f"Embeddings: {'✓' if embeddings_ok else '✗'}")
    print(f"Full Pipeline: {'✓' if pipeline_ok else '✗'}")
