#!/usr/bin/env python3
"""
Fallback Response System for TeleConnect RAG Bot
Provides helpful responses when AI services are unavailable
"""

import re
from typing import Dict, List, Any, Optional

class FallbackResponseSystem:
    """
    Provides intelligent fallback responses when AI services are unavailable
    """
    
    def __init__(self):
        """Initialize the fallback response system"""
        self.response_patterns = self._initialize_patterns()
        self.general_responses = self._initialize_general_responses()
    
    def _initialize_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize response patterns for common queries"""
        return {
            "internet_slow": {
                "patterns": [
                    r"internet.*slow", r"connection.*slow", r"speed.*slow",
                    r"slow.*internet", r"slow.*connection", r"bandwidth"
                ],
                "response": """I understand you're experiencing slow internet speeds. Here are some quick troubleshooting steps:

1. **Restart your equipment**: Unplug your modem and router for 30 seconds, then plug them back in
2. **Check your plan**: Verify you're getting the speed you're paying for at speedtest.teleconnect.com
3. **Reduce network usage**: Pause downloads, streaming, or other high-bandwidth activities
4. **Check for interference**: Move closer to your router or check for obstacles

If these steps don't help, please contact our technical support at 1-800-TECH-HELP (**************) for further assistance.""",
                "intent": {"category": "technical_support", "urgency": "medium", "requires_human": False},
                "followup": ["Would you like me to connect you with technical support?", "Do you need help with anything else?"]
            },
            
            "billing": {
                "patterns": [
                    r"bill", r"charge", r"payment", r"cost", r"price", r"fee",
                    r"invoice", r"account", r"balance"
                ],
                "response": """For billing inquiries, I can help you with general information:

**Common Billing Questions:**
- View your bill online at teleconnect.com/mybill
- Set up autopay to avoid late fees
- Payment methods: online, phone, or mail
- Billing cycle: Monthly on the same date each month

**For specific billing questions or disputes:**
Please contact our billing department at 1-800-BILLING (**************) or visit teleconnect.com/billing

They can help with payment arrangements, bill explanations, and account adjustments.""",
                "intent": {"category": "billing_inquiry", "urgency": "medium", "requires_human": True},
                "followup": ["Would you like me to connect you with billing support?", "Do you need help with your account online?"]
            },
            
            "mobile_signal": {
                "patterns": [
                    r"signal", r"reception", r"bars", r"coverage", r"phone.*not.*work",
                    r"no.*service", r"dropped.*call", r"call.*quality"
                ],
                "response": """I can help with mobile signal issues. Try these steps:

**Immediate Steps:**
1. **Restart your phone**: Turn it off and on again
2. **Check signal strength**: Look for signal bars or go to Settings > About Phone > Network
3. **Move to a different location**: Signal can vary by location
4. **Toggle airplane mode**: Turn it on for 10 seconds, then off

**If problems persist:**
- Check our coverage map at teleconnect.com/coverage
- Ensure your phone software is updated
- Remove and reinsert your SIM card

For persistent signal issues, contact technical support at 1-800-TECH-HELP (**************).""",
                "intent": {"category": "technical_support", "urgency": "medium", "requires_human": False},
                "followup": ["Is the signal issue in a specific location?", "Would you like me to connect you with technical support?"]
            },
            
            "plan_change": {
                "patterns": [
                    r"change.*plan", r"upgrade", r"downgrade", r"switch.*plan",
                    r"different.*plan", r"plan.*option"
                ],
                "response": """I'd be happy to help you explore plan options:

**Current Plan Options:**
- **Basic**: 25 Mbps internet, unlimited talk/text
- **Standard**: 100 Mbps internet, unlimited everything
- **Premium**: 500 Mbps internet, unlimited everything + premium features

**To change your plan:**
1. Visit teleconnect.com/plans to compare options
2. Call customer service at 1-800-TELECON (**************)
3. Visit a TeleConnect store near you

**Important Notes:**
- Plan changes typically take effect on your next billing cycle
- Some changes may require new equipment
- Early termination fees may apply to contract plans""",
                "intent": {"category": "service_change", "urgency": "low", "requires_human": True},
                "followup": ["Would you like me to connect you with customer service?", "Do you need help finding a store location?"]
            },
            
            "outage": {
                "patterns": [
                    r"outage", r"down", r"not.*working", r"service.*out",
                    r"internet.*out", r"no.*internet"
                ],
                "response": """If you're experiencing a service outage:

**First, check our status page:**
Visit teleconnect.com/status to see if there are known outages in your area.

**If no outage is reported:**
1. Check all cable connections
2. Restart your modem and router
3. Check if neighbors are also affected
4. Try connecting a device directly to the modem

**Report an outage:**
- Online: teleconnect.com/report-outage
- Phone: 1-800-TELECON (**************)
- Mobile app: TeleConnect Support

We'll send updates about restoration progress to your registered phone number.""",
                "intent": {"category": "technical_support", "urgency": "high", "requires_human": False},
                "followup": ["Have you checked our status page?", "Would you like me to connect you with technical support?"]
            }
        }
    
    def _initialize_general_responses(self) -> List[str]:
        """Initialize general helpful responses"""
        return [
            """I'm currently experiencing technical difficulties with my AI processing, but I can still help you with basic information:

**For immediate assistance:**
- Customer Service: 1-800-TELECON (**************)
- Technical Support: 1-800-TECH-HELP (**************)
- Billing Questions: 1-800-BILLING (**************)

**Online Resources:**
- Account management: teleconnect.com/myaccount
- Support articles: support.teleconnect.com
- Service status: teleconnect.com/status

Please let me know what specific issue you're experiencing, and I'll do my best to provide helpful guidance."""
        ]
    
    def generate_fallback_response(self, query: str, session_id: str = None) -> Dict[str, Any]:
        """
        Generate a fallback response when AI services are unavailable
        
        Args:
            query: User's query
            session_id: Session identifier
            
        Returns:
            Response dictionary similar to AI-generated responses
        """
        query_lower = query.lower()
        
        # Try to match specific patterns
        for category, pattern_data in self.response_patterns.items():
            for pattern in pattern_data["patterns"]:
                if re.search(pattern, query_lower):
                    return {
                        "response": pattern_data["response"],
                        "session_id": session_id,
                        "intent": pattern_data["intent"],
                        "context_sources": ["Fallback Response System"],
                        "followup_questions": pattern_data["followup"],
                        "conversation_summary": f"Provided fallback response for {category}",
                        "fallback_mode": True
                    }
        
        # If no specific pattern matches, use general response
        return {
            "response": self.general_responses[0],
            "session_id": session_id,
            "intent": {"category": "general_info", "urgency": "medium", "requires_human": True},
            "context_sources": ["Fallback Response System"],
            "followup_questions": [
                "What specific issue are you experiencing?",
                "Would you like me to connect you with a human agent?"
            ],
            "conversation_summary": "Provided general fallback response",
            "fallback_mode": True
        }
    
    def is_emergency_query(self, query: str) -> bool:
        """
        Check if the query indicates an emergency
        
        Args:
            query: User's query
            
        Returns:
            True if query seems to indicate an emergency
        """
        emergency_patterns = [
            r"emergency", r"urgent", r"911", r"help.*now", r"critical",
            r"no.*service.*emergency", r"can't.*call.*911"
        ]
        
        query_lower = query.lower()
        return any(re.search(pattern, query_lower) for pattern in emergency_patterns)
    
    def get_emergency_response(self, session_id: str = None) -> Dict[str, Any]:
        """
        Get emergency response
        
        Args:
            session_id: Session identifier
            
        Returns:
            Emergency response dictionary
        """
        return {
            "response": """🚨 **EMERGENCY ASSISTANCE** 🚨

If this is a life-threatening emergency, **CALL 911 IMMEDIATELY**.

If you cannot make calls due to service issues:
- Try calling from a different phone or location
- Use a landline if available
- Ask a neighbor for help
- Go to the nearest hospital or fire station

**For TeleConnect emergency service issues:**
Call 1-800-EMERGENCY or visit your nearest TeleConnect store.

**Non-emergency technical support:**
1-800-TECH-HELP (**************)

Your safety is our top priority.""",
            "session_id": session_id,
            "intent": {"category": "emergency", "urgency": "critical", "requires_human": True},
            "context_sources": ["Emergency Response System"],
            "followup_questions": [
                "Do you need immediate emergency services?",
                "Can you access a working phone?"
            ],
            "conversation_summary": "Provided emergency assistance information",
            "fallback_mode": True,
            "emergency": True
        }

# Example usage
if __name__ == "__main__":
    fallback = FallbackResponseSystem()
    
    # Test queries
    test_queries = [
        "My internet is very slow",
        "I need to change my plan",
        "There's an emergency and I can't call 911",
        "What are my billing charges?",
        "Random question about something"
    ]
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        if fallback.is_emergency_query(query):
            response = fallback.get_emergency_response()
        else:
            response = fallback.generate_fallback_response(query)
        print(f"Response: {response['response'][:100]}...")
        print(f"Category: {response['intent']['category']}")
