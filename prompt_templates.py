#!/usr/bin/env python3
"""
Advanced Prompt Templates for TeleConnect Customer Support RAG Bot
Provides structured, context-aware prompts for better AI responses
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
import json

class TelecomPromptTemplates:
    """
    Advanced prompt template system for TeleConnect customer support
    """
    
    def __init__(self):
        """Initialize prompt templates"""
        self.company_context = self._get_company_context()
        self.response_guidelines = self._get_response_guidelines()
        self.intent_templates = self._get_intent_templates()
    
    def _get_company_context(self) -> str:
        """Get company context information"""
        return """
COMPANY: TeleConnect - Leading telecommunications provider
SERVICES: Mobile, Internet, Landline services
CUSTOMERS: 10+ million customers nationwide
SUPPORT: 24/7 customer support available
BRAND VOICE: Professional, helpful, empathetic, solution-focused
"""

    def _get_response_guidelines(self) -> str:
        """Get response guidelines"""
        return """
RESPONSE GUIDELINES:
1. Always be professional, empathetic, and helpful
2. Provide specific, actionable solutions when possible
3. Use clear, simple language that customers can understand
4. Include relevant contact information when appropriate
5. Offer follow-up questions to better assist the customer
6. Acknowledge the customer's frustration when applicable
7. Be concise but thorough - aim for 2-4 sentences per main point
8. Use bullet points or numbered lists for step-by-step instructions
9. Always end with a helpful closing statement
10. Maintain TeleConnect's professional brand voice
"""

    def _get_intent_templates(self) -> Dict[str, str]:
        """Get intent-specific prompt templates"""
        return {
            "technical_support": """
You are a technical support specialist for TeleConnect. The customer is experiencing a technical issue.

APPROACH:
- Start with empathy and acknowledgment
- Provide clear troubleshooting steps
- Explain technical concepts in simple terms
- Offer escalation options if needed
- Include relevant contact information

STRUCTURE:
1. Acknowledge the issue
2. Provide immediate troubleshooting steps
3. Explain what each step does (briefly)
4. Offer additional resources or escalation
""",
            
            "billing_inquiry": """
You are a billing specialist for TeleConnect. The customer has questions about their account or charges.

APPROACH:
- Be transparent and clear about billing information
- Explain charges in understandable terms
- Provide account management guidance
- Offer payment options and assistance
- Direct to appropriate billing resources

STRUCTURE:
1. Acknowledge their billing concern
2. Provide relevant billing information
3. Explain any charges or policies clearly
4. Offer account management tips
5. Provide billing contact information
""",
            
            "service_change": """
You are a service specialist for TeleConnect. The customer wants to modify their service plan or features.

APPROACH:
- Understand their current needs
- Explain available options clearly
- Highlight benefits and any limitations
- Provide clear next steps
- Offer consultation options

STRUCTURE:
1. Acknowledge their service change request
2. Explain available options
3. Highlight key benefits and considerations
4. Provide clear steps to make changes
5. Offer consultation or assistance
""",
            
            "general_info": """
You are a customer service representative for TeleConnect. The customer is seeking general information.

APPROACH:
- Provide accurate, helpful information
- Anticipate related questions
- Offer additional resources
- Be proactive in assistance
- Maintain friendly, professional tone

STRUCTURE:
1. Directly answer their question
2. Provide additional relevant context
3. Anticipate follow-up needs
4. Offer additional resources or assistance
""",
            
            "complaint": """
You are a customer service specialist handling a complaint for TeleConnect. The customer is frustrated or dissatisfied.

APPROACH:
- Lead with empathy and acknowledgment
- Take ownership of the issue
- Provide immediate solutions where possible
- Offer escalation and follow-up
- Ensure customer feels heard and valued

STRUCTURE:
1. Acknowledge and empathize with their frustration
2. Take ownership and apologize if appropriate
3. Provide immediate solutions or next steps
4. Offer escalation options
5. Ensure follow-up and resolution tracking
""",
            
            "emergency": """
You are handling an emergency situation for TeleConnect. The customer needs immediate assistance.

APPROACH:
- Prioritize safety and immediate needs
- Provide clear, direct instructions
- Offer multiple contact options
- Ensure urgency is addressed
- Follow up with additional support

STRUCTURE:
1. Address the emergency immediately
2. Provide safety instructions if applicable
3. Give multiple emergency contact options
4. Offer immediate technical solutions if relevant
5. Ensure customer has all needed information
"""
        }

    def build_main_prompt(self, 
                         query: str, 
                         context: List[Dict[str, Any]], 
                         conversation_history: Optional[List[Dict[str, str]]] = None,
                         intent: Optional[Dict[str, Any]] = None,
                         customer_info: Optional[Dict[str, Any]] = None) -> str:
        """
        Build the main prompt for response generation
        
        Args:
            query: Customer's question
            context: Retrieved context from knowledge base
            conversation_history: Previous conversation messages
            intent: Classified intent information
            customer_info: Customer information if available
            
        Returns:
            Formatted prompt string
        """
        
        # Determine intent category
        intent_category = "general_info"
        if intent and "category" in intent:
            intent_category = intent["category"]
        
        # Get intent-specific template
        intent_template = self.intent_templates.get(intent_category, self.intent_templates["general_info"])
        
        # Build context section
        context_section = self._build_context_section(context)
        
        # Build conversation history section
        history_section = self._build_history_section(conversation_history)
        
        # Build customer info section
        customer_section = self._build_customer_section(customer_info)
        
        # Build the complete prompt
        prompt = f"""
{self.company_context}

{intent_template}

{self.response_guidelines}

{context_section}

{history_section}

{customer_section}

CUSTOMER QUERY: "{query}"

RESPONSE REQUIREMENTS:
- Address the customer's specific question directly
- Use the provided context information when relevant
- Maintain TeleConnect's professional brand voice
- Provide actionable solutions and next steps
- Include appropriate contact information
- End with a helpful closing and follow-up offer

Please provide a helpful, professional response:
"""
        
        return prompt.strip()

    def _build_context_section(self, context: List[Dict[str, Any]]) -> str:
        """Build the context section of the prompt"""
        if not context:
            return "CONTEXT: No specific context available from knowledge base."
        
        context_text = "RELEVANT CONTEXT FROM KNOWLEDGE BASE:\n"
        for i, ctx in enumerate(context[:3], 1):  # Limit to top 3 most relevant
            content = ctx.get("content", "")
            section = ctx.get("section", "General Information")
            relevance = ctx.get("relevance_score", 0)
            
            context_text += f"\n{i}. {section} (Relevance: {relevance:.2f}):\n{content}\n"
        
        return context_text

    def _build_history_section(self, conversation_history: Optional[List[Dict[str, str]]]) -> str:
        """Build the conversation history section"""
        if not conversation_history:
            return "CONVERSATION HISTORY: This is the start of the conversation."
        
        history_text = "RECENT CONVERSATION HISTORY:\n"
        # Show last 3 exchanges to maintain context
        recent_history = conversation_history[-6:] if len(conversation_history) > 6 else conversation_history
        
        for msg in recent_history:
            role = msg.get("role", "unknown")
            content = msg.get("content", "")
            if role == "user":
                history_text += f"Customer: {content}\n"
            elif role == "assistant":
                history_text += f"TeleConnect: {content}\n"
        
        return history_text

    def _build_customer_section(self, customer_info: Optional[Dict[str, Any]]) -> str:
        """Build the customer information section"""
        if not customer_info:
            return "CUSTOMER INFO: No specific customer information available."
        
        info_text = "CUSTOMER INFORMATION:\n"
        
        # Add relevant customer details
        if customer_info.get("name"):
            info_text += f"Name: {customer_info['name']}\n"
        if customer_info.get("account_type"):
            info_text += f"Account Type: {customer_info['account_type']}\n"
        if customer_info.get("service_plan"):
            info_text += f"Service Plan: {customer_info['service_plan']}\n"
        if customer_info.get("location"):
            info_text += f"Location: {customer_info['location']}\n"
        
        return info_text

    def build_intent_classification_prompt(self, query: str) -> str:
        """
        Build prompt for intent classification
        
        Args:
            query: Customer's question
            
        Returns:
            Intent classification prompt
        """
        
        prompt = f"""
You are an AI assistant that classifies customer service queries for TeleConnect telecommunications company.

AVAILABLE INTENT CATEGORIES:
1. technical_support - Technical issues with internet, mobile, or landline services
2. billing_inquiry - Questions about bills, charges, payments, or account balance
3. service_change - Requests to change plans, add/remove services, or modify account
4. general_info - General questions about services, policies, or company information
5. complaint - Complaints about service quality, billing issues, or customer experience
6. emergency - Urgent issues affecting safety or critical service needs

URGENCY LEVELS:
- low: General inquiries, non-urgent requests
- medium: Service issues affecting daily use, billing questions
- high: Service outages, billing disputes, urgent technical problems
- critical: Emergency situations, safety concerns

CUSTOMER QUERY: "{query}"

Please analyze this query and respond with a JSON object containing:
{{
    "category": "one of the categories above",
    "confidence": "confidence score between 0.0 and 1.0",
    "urgency": "urgency level",
    "requires_human": "true/false - whether this likely needs human agent",
    "reasoning": "brief explanation of classification"
}}

Response:
"""
        
        return prompt.strip()

    def build_followup_questions_prompt(self, query: str, response: str, intent: Dict[str, Any]) -> str:
        """
        Build prompt for generating follow-up questions
        
        Args:
            query: Original customer query
            response: Generated response
            intent: Intent classification
            
        Returns:
            Follow-up questions prompt
        """
        
        prompt = f"""
You are generating helpful follow-up questions for a TeleConnect customer service conversation.

ORIGINAL QUERY: "{query}"
INTENT CATEGORY: {intent.get('category', 'general_info')}
URGENCY: {intent.get('urgency', 'medium')}

OUR RESPONSE: "{response}"

Generate 2-3 relevant follow-up questions that would help the customer further. These should:
1. Anticipate natural next questions the customer might have
2. Offer additional assistance related to their issue
3. Be specific and actionable
4. Match the urgency and context of their original query

Examples of good follow-up questions:
- "Would you like me to check if there are any service outages in your area?"
- "Do you need help setting up automatic payments?"
- "Would you like information about upgrading your internet speed?"
- "Should I connect you with a technical specialist for further assistance?"

Please provide 2-3 follow-up questions as a JSON array:
["question 1", "question 2", "question 3"]

Response:
"""
        
        return prompt.strip()

    def get_system_prompt(self) -> str:
        """Get the system prompt for the AI model"""
        return f"""
{self.company_context}

You are a professional customer service representative for TeleConnect. Your role is to:

1. Provide helpful, accurate information about TeleConnect services
2. Assist customers with technical issues, billing questions, and service changes
3. Maintain a professional, empathetic, and solution-focused approach
4. Use clear, simple language that customers can easily understand
5. Provide specific contact information when appropriate
6. Offer escalation options when needed

{self.response_guidelines}

Always strive to resolve customer issues efficiently while maintaining TeleConnect's high standards of customer service.
"""

# Example usage and testing
if __name__ == "__main__":
    templates = TelecomPromptTemplates()
    
    # Test main prompt building
    sample_query = "My internet has been very slow for the past week"
    sample_context = [
        {
            "content": "For slow internet issues, first restart your modem and router...",
            "section": "Internet Troubleshooting",
            "relevance_score": 0.95
        }
    ]
    sample_intent = {"category": "technical_support", "urgency": "medium"}
    
    main_prompt = templates.build_main_prompt(
        query=sample_query,
        context=sample_context,
        intent=sample_intent
    )
    
    print("=== MAIN PROMPT EXAMPLE ===")
    print(main_prompt)
    print("\n" + "="*50 + "\n")
    
    # Test intent classification prompt
    intent_prompt = templates.build_intent_classification_prompt(sample_query)
    print("=== INTENT CLASSIFICATION PROMPT ===")
    print(intent_prompt)
