#!/usr/bin/env python3
"""
Debug the fallback response system
"""

import re

def test_fallback_patterns():
    """Test fallback pattern matching"""
    
    print("=== Testing Fallback Pattern Matching ===\n")
    
    # Test patterns
    patterns = {
        "internet_slow": [
            r"internet.*slow", r"connection.*slow", r"speed.*slow",
            r"slow.*internet", r"slow.*connection", r"bandwidth"
        ]
    }
    
    test_queries = [
        "My internet is very slow",
        "Internet connection is slow", 
        "Slow internet speed",
        "Connection speed is slow",
        "Bandwidth issues",
        "Random unrelated query"
    ]
    
    print("Testing pattern matching:")
    for query in test_queries:
        query_lower = query.lower()
        print(f"\nQuery: '{query}'")
        print(f"Lowercase: '{query_lower}'")
        
        matched = False
        for pattern in patterns["internet_slow"]:
            match = re.search(pattern, query_lower)
            if match:
                print(f"  ✅ Matched pattern: '{pattern}' -> {match.group()}")
                matched = True
                break
            else:
                print(f"  ❌ No match for: '{pattern}'")
        
        if not matched:
            print(f"  ⚠️  No patterns matched for this query")

def test_fallback_system():
    """Test the actual fallback system"""
    
    print("\n=== Testing Fallback System ===\n")
    
    try:
        from fallback_responses import FallbackResponseSystem
        
        fallback = FallbackResponseSystem()
        
        test_queries = [
            "My internet is very slow",
            "I need help with my bill",
            "Random query that shouldn't match"
        ]
        
        for query in test_queries:
            print(f"Query: '{query}'")
            result = fallback.generate_fallback_response(query)
            
            print(f"  Category: {result['intent']['category']}")
            print(f"  Fallback mode: {result.get('fallback_mode', False)}")
            print(f"  Response preview: {result['response'][:100]}...")
            print()
            
    except Exception as e:
        print(f"Error testing fallback system: {e}")
        import traceback
        traceback.print_exc()

def test_rag_pipeline_fallback():
    """Test the RAG pipeline fallback integration"""
    
    print("=== Testing RAG Pipeline Fallback ===\n")
    
    try:
        from rag_pipeline import TelecomRAGPipeline
        
        rag = TelecomRAGPipeline()
        
        # This should trigger fallback due to quota exceeded
        result = rag.process_query("My internet is very slow")
        
        print(f"Response type: {type(result)}")
        print(f"Fallback mode: {result.get('fallback_mode', False)}")
        print(f"Intent category: {result['intent']['category']}")
        print(f"Response preview: {result['response'][:150]}...")
        
        if result.get('fallback_mode'):
            print("✅ Fallback system is working!")
        else:
            print("❌ Fallback system not triggered")
            
    except Exception as e:
        print(f"Error testing RAG pipeline: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fallback_patterns()
    test_fallback_system()
    test_rag_pipeline_fallback()
