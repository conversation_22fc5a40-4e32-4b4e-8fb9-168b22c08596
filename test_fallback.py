#!/usr/bin/env python3
"""
Test the fallback response system
"""

from fallback_responses import FallbackResponseSystem

def test_fallback_system():
    """Test the fallback response system"""
    fallback = FallbackResponseSystem()
    
    test_queries = [
        "My internet is very slow",
        "I need help with my bill",
        "My phone has no signal",
        "I want to change my plan",
        "There's an outage in my area",
        "Emergency! I can't call 911",
        "Random question about something else"
    ]
    
    print("=== Testing Fallback Response System ===\n")
    
    for query in test_queries:
        print(f"Query: {query}")
        
        if fallback.is_emergency_query(query):
            print("🚨 EMERGENCY DETECTED")
            response = fallback.get_emergency_response("test-session")
        else:
            response = fallback.generate_fallback_response(query, "test-session")
        
        print(f"Category: {response['intent']['category']}")
        print(f"Urgency: {response['intent']['urgency']}")
        print(f"Fallback Mode: {response.get('fallback_mode', False)}")
        print(f"Response: {response['response'][:150]}...")
        print(f"Follow-up: {response['followup_questions']}")
        print("-" * 80)

if __name__ == "__main__":
    test_fallback_system()
