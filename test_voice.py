#!/usr/bin/env python3
"""
Test script for voice functionality
"""

import sys
import logging
from voice_output import VoiceOutputHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_voice_output():
    """Test voice output functionality"""
    print("=== Testing Voice Output ===")
    
    # Initialize voice handler
    print("1. Initializing voice handler...")
    handler = VoiceOutputHandler()
    
    # Check if TTS is available
    print("2. Checking TTS availability...")
    if not handler.is_tts_available():
        print("❌ TTS not available")
        return False
    
    print("✅ TTS engine initialized successfully")
    
    # Get available voices
    print("3. Getting available voices...")
    voices = handler.get_available_voices()
    print(f"Available voices: {len(voices)}")
    for i, voice in enumerate(voices):
        print(f"  {i+1}. {voice['name']} (ID: {voice['id']})")
    
    # Test speech
    print("4. Testing speech output...")
    test_text = "Hello! This is a test of the voice output system. Can you hear me clearly?"
    
    print(f"Speaking: {test_text}")
    success = handler.speak_text(test_text)
    
    if success:
        print("✅ Speech test successful")
        return True
    else:
        print("❌ Speech test failed")
        return False

def test_voice_settings():
    """Test voice settings"""
    print("\n=== Testing Voice Settings ===")
    
    handler = VoiceOutputHandler()
    
    if not handler.is_tts_available():
        print("❌ TTS not available for settings test")
        return False
    
    # Test different settings
    print("1. Testing different speech rates...")
    for rate in [150, 200, 250]:
        print(f"Setting rate to {rate} WPM...")
        handler.set_voice_settings(rate=rate)
        handler.speak_text(f"This is speech at {rate} words per minute.")
    
    # Reset to default
    handler.set_voice_settings(rate=180)
    print("✅ Voice settings test completed")
    return True

if __name__ == "__main__":
    print("Voice Output Test Script")
    print("=" * 40)
    
    try:
        # Test basic voice output
        voice_success = test_voice_output()
        
        if voice_success:
            # Test voice settings
            settings_success = test_voice_settings()
            
            if settings_success:
                print("\n🎉 All voice tests passed!")
                sys.exit(0)
            else:
                print("\n⚠️ Voice settings test failed")
                sys.exit(1)
        else:
            print("\n❌ Voice output test failed")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test script error: {e}")
        logger.exception("Test script exception")
        sys.exit(1)
